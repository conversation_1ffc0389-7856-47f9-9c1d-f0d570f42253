import React, { useRef, useCallback, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { KpiData, DashboardFilters } from '@/types/dashboard';
import { UserProfile, ProfileType } from '@/types/profile';
import { useKPIDrawer } from '@/hooks/useKPIDrawer';
import KpiBentoCard from './KpiBentoCard';

interface KpiBentoGridProps {
  kpis: KpiData[];
  filters: DashboardFilters;
  onTogglePriority: (kpiId: string) => void;
  onRemoveKpi?: (kpiId: string) => void;
  periodData?: {
    currentDate: string;
    isSimulated: boolean;
    periodDescription?: string | null;
  };
  // Week 5: Profile-aware props (optional for backward compatibility)
  userProfile?: UserProfile | null;
  profileType?: ProfileType;
  enableProfileFiltering?: boolean;
}

const KpiBentoGrid: React.FC<KpiBentoGridProps> = ({
  kpis,
  filters,
  onTogglePriority,
  onRemoveKpi,
  periodData,
  // Week 5: Profile-aware props
  userProfile,
  profileType,
  enableProfileFiltering = false
}) => {


  // Get drawer state for card selection
  const { selectedCardId, isCardSelected } = useKPIDrawer();
  // Week 5: Profile-aware KPI filtering
  const filteredKpis = useMemo(() => {
    if (!enableProfileFiltering || (!userProfile && !profileType)) {
      return kpis; // Return all KPIs if profile filtering is disabled
    }

    const currentProfileType = userProfile?.profileType || profileType;
    if (!currentProfileType) {
      return kpis;
    }

    // Get profile-specific KPI recommendations
    const profileRecommendations = {
      'CEO': ['spread_income_detailed', 'margem_liquida_operacional'],
      'CFO': ['margem_liquida_operacional', 'custo_por_transacao'],
      'Risk_Manager': ['tempo_processamento_medio'],
      'Trader': ['spread_income_detailed', 'tempo_processamento_medio'],
      'Operations': ['custo_por_transacao', 'tempo_processamento_medio']
    };

    const recommendedKpiIds = profileRecommendations[currentProfileType] || [];

    // Filter KPIs based on profile recommendations or user's selected KPIs
    const userSelectedKpis = userProfile?.selectedKpis || [];
    const relevantKpiIds = userSelectedKpis.length > 0 ? userSelectedKpis : recommendedKpiIds;

    if (relevantKpiIds.length === 0) {
      return kpis; // Return all if no specific recommendations
    }

    // Filter and prioritize KPIs
    const filtered = kpis.filter(kpi =>
      relevantKpiIds.includes(kpi.id) ||
      kpi.is_priority ||
      kpi.isPriority
    );

    // Sort by relevance: user selected > recommended > priority > others
    return filtered.sort((a, b) => {
      const aIsUserSelected = userSelectedKpis.includes(a.id);
      const bIsUserSelected = userSelectedKpis.includes(b.id);
      const aIsRecommended = recommendedKpiIds.includes(a.id);
      const bIsRecommended = recommendedKpiIds.includes(b.id);
      const aIsPriority = a.is_priority || a.isPriority;
      const bIsPriority = b.is_priority || b.isPriority;

      if (aIsUserSelected && !bIsUserSelected) return -1;
      if (!aIsUserSelected && bIsUserSelected) return 1;
      if (aIsRecommended && !bIsRecommended) return -1;
      if (!aIsRecommended && bIsRecommended) return 1;
      if (aIsPriority && !bIsPriority) return -1;
      if (!aIsPriority && bIsPriority) return 1;

      return 0;
    });
  }, [kpis, enableProfileFiltering, userProfile, profileType]);

  // Event delegation for star clicks - COMPLETELY SEPARATE FROM ANIMATIONS
  const lastClickTime = useRef<number>(0);
  const DEBOUNCE_MS = 500;
  
  const handleGridClick = useCallback((e: React.MouseEvent) => {
    // Find the closest element with data-kpi-action
    const target = (e.target as HTMLElement).closest('[data-kpi-action]');
    if (!target || target.getAttribute('data-kpi-action') !== 'toggle-priority') {
      return;
    }
    
    // CRITICAL: Stop propagation to prevent card drawer from opening
    e.preventDefault();
    e.stopPropagation();
    
    // Use stopImmediatePropagation only if available
    if (e.stopImmediatePropagation) {
      e.stopImmediatePropagation();
    }
    
    const kpiId = target.getAttribute('data-kpi-id');
    if (!kpiId) return;
    
    const now = Date.now();
    const timeSinceLastClick = now - lastClickTime.current;
    
    // Robust debouncing
    if (timeSinceLastClick < DEBOUNCE_MS) {
      console.log(`⏳ GRID: Click ignored for ${kpiId} - ${timeSinceLastClick}ms since last (need ${DEBOUNCE_MS}ms)`);
      return;
    }
    
    console.log(`⭐ GRID: Star clicked for KPI: ${kpiId} (${timeSinceLastClick}ms since last)`);
    lastClickTime.current = now;
    
    // Execute toggle - this is completely separated from animations
    onTogglePriority(kpiId);
  }, [onTogglePriority, DEBOUNCE_MS]);
  // Sort KPIs: priority KPIs first, then others
  // Week 5: Use filtered KPIs instead of all KPIs
  const sortedKpis = React.useMemo(() => {
    return [...filteredKpis].sort((a, b) => {
      // Check both field variants for compatibility
      const aPriority = a.is_priority || a.isPriority;
      const bPriority = b.is_priority || b.isPriority;

      if (aPriority && !bPriority) return -1;
      if (!aPriority && bPriority) return 1;
      return 0;
    });
  }, [filteredKpis]);

  // Dynamic grid layout that adapts to number of KPIs and priority status
  const getGridClassName = (kpi: KpiData, index: number, totalKpis: number) => {
    const isPriority = kpi.is_priority || kpi.isPriority;

    if (totalKpis <= 2) {
      // 1-2 KPIs: Half width cards for better layout
      return "col-span-12 md:col-span-6 row-span-2";
    } else if (totalKpis <= 4) {
      // 3-4 KPIs: Priority gets larger cards
      if (isPriority) {
        return "col-span-12 md:col-span-8 row-span-2";
      } else {
        return "col-span-12 md:col-span-4 row-span-1";
      }
    } else if (totalKpis <= 6) {
      // 5-6 KPIs: Bento layout - priority KPIs get larger cards
      if (isPriority) {
        return "col-span-12 md:col-span-6 row-span-2";
      } else {
        return "col-span-6 md:col-span-3 row-span-1";
      }
    } else {
      // 7+ KPIs: Priority KPIs get medium size, others small
      if (isPriority) {
        return "col-span-6 md:col-span-4 row-span-2";
      } else {
        return "col-span-6 md:col-span-3 row-span-1";
      }
    }
  };



  return (
    <div className="w-full px-4 md:px-6 lg:px-8" onClick={handleGridClick}>
      <AnimatePresence mode="popLayout">
        <motion.div
          className="grid grid-cols-12 gap-4 md:gap-6 auto-rows-[minmax(180px,_1fr)]"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          {sortedKpis.map((kpi, index) => (
            <motion.div
              key={kpi.id}
              className={cn(
                getGridClassName(kpi, index, sortedKpis.length),
                "min-h-0" // Allow cards to size based on content
              )}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              layout
              transition={{ 
                layout: { 
                  duration: 0.6,
                  ease: "easeInOut"
                },
                opacity: { duration: 0.3 },
                y: { duration: 0.4, delay: index * 0.02 }
              }}
            >
              <KpiBentoCard
                kpi={kpi}
                filters={filters}
                onRemoveKpi={onRemoveKpi}
                isLarge={kpi.is_priority || kpi.isPriority}
                isSelected={isCardSelected(kpi.id)}
                periodData={periodData}
              />
            </motion.div>
          ))}
        </motion.div>
      </AnimatePresence>
    </div>
  );
};

export default KpiBentoGrid;