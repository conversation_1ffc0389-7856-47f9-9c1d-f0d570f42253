"""
Dashboard API Endpoints - DataHero4
===================================

API endpoints for dashboard functionality including KPI data retrieval.
These endpoints integrate with the existing FastAPI application.
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

from fastapi import APIRouter, HTTPException, Query, Depends
from pydantic import BaseModel, Field

from src.services.hybrid_kpi_service import get_hybrid_kpi_service
from src.services.smart_query_router import get_smart_query_router
from src.services.profile_detector import get_profile_detector
from src.config.client_config import CLIENT_CONFIG

logger = logging.getLogger(__name__)

# Create router for dashboard endpoints
dashboard_router = APIRouter(prefix="/api", tags=["dashboard"])

# Supported profile types
SUPPORTED_PROFILES = ["CEO", "CFO", "Risk_Manager", "Trader", "Operations"]


# Response models
class KpiAlert(BaseModel):
    """KPI alert configuration."""
    type: str = Field(..., description="Alert type: 'above' or 'below'")
    threshold: float = Field(..., description="Alert threshold value")
    message: Optional[str] = Field(None, description="Alert message")


class KpiData(BaseModel):
    """KPI data response model."""
    id: str = Field(..., description="KPI identifier")
    title: str = Field(..., description="KPI display name")
    description: str = Field(..., description="KPI description")
    currentValue: float = Field(..., description="Current KPI value")
    format: str = Field(..., description="Value format: currency, percentage, number")
    changePercent: Optional[float] = Field(None, description="Percentage change from previous period")
    trend: str = Field(..., description="Trend direction: up, down, stable")
    chartType: str = Field(..., description="Chart type: line, area, bar")
    chartData: List[Dict[str, Any]] = Field(..., description="Chart data points")
    alert: Optional[KpiAlert] = Field(None, description="Alert configuration")
    isPriority: bool = Field(False, description="Whether KPI is priority")
    order: int = Field(0, description="Display order")
    category: str = Field(..., description="KPI category")
    unit: Optional[str] = Field(None, description="Unit of measurement")
    frequency: Optional[str] = Field(None, description="Update frequency")


# LEGACY RESPONSE MODELS REMOVED:
# - DashboardKpisResponse (replaced by personalized KPI response)
# - SingleKpiResponse (individual KPI calculation no longer needed)
# - DashboardSummaryResponse (replaced by personalized dashboard)


# LEGACY DEPENDENCY REMOVED: get_kpi_service_dependency
# KPI services are now handled through the hybrid architecture


# TEMPORARY ENDPOINT: /dashboard/kpis
# This endpoint provides backward compatibility while frontend is updated
@dashboard_router.get("/dashboard/kpis", summary="Get dashboard KPIs (legacy compatibility)")
async def get_dashboard_kpis_legacy(
    sector: str = Query("cambio", description="Business sector"),
    client_id: str = Query("L2M", description="Client identifier"),
    timeframe: str = Query("week", description="Data timeframe"),
    currency: str = Query("all", description="Currency filter"),
    priority_only: bool = Query(True, description="Return only priority KPIs"),
    category: Optional[str] = Query(None, description="Category filter")
):
    """
    Legacy endpoint for dashboard KPIs - provides backward compatibility.

    **DEPRECATED**: This endpoint is deprecated and will be removed.
    Use /personalized-kpis instead for better performance and profile-aware KPIs.
    """
    try:
        logger.warning("⚠️ Using deprecated /dashboard/kpis endpoint - update frontend to use /personalized-kpis")

        # Use personalized KPIs with default user
        request = PersonalizedKpiRequest(
            user_id="default_user",
            profile_type="CEO",  # Default to CEO profile
            timeframe=timeframe,
            currency=currency
        )

        # Get services
        hybrid_service = get_hybrid_kpi_service()
        router_service = get_smart_query_router()
        profile_service = get_profile_detector()

        # Call the personalized KPIs endpoint internally
        result = await get_personalized_kpis(
            request=request,
            hybrid_service=hybrid_service,
            router_service=router_service,
            profile_service=profile_service
        )

        # Convert to legacy format
        legacy_response = {
            "kpis": result["kpis"],
            "total_kpis": result["total_kpis"],
            "sector": sector,
            "client_id": client_id,
            "timeframe": timeframe,
            "currency": currency,
            "priority_only": priority_only,
            "metadata": {
                "deprecated": True,
                "message": "This endpoint is deprecated. Use /personalized-kpis instead.",
                "new_endpoint": "/api/personalized-kpis"
            }
        }

        return legacy_response

    except Exception as e:
        logger.error(f"❌ Error in legacy dashboard KPIs endpoint: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                'error': 'legacy_dashboard_kpis_error',
                'message': str(e),
                'deprecated': True,
                'new_endpoint': '/api/personalized-kpis'
            }
        )


# LEGACY ENDPOINT REMOVED: /kpis/{kpi_id}/calculate
# Individual KPI calculation is now handled through the personalized
# KPI system which calculates profile-specific KPIs in batches


# LEGACY ENDPOINT REMOVED: /kpis/available
# KPI selection is now profile-based through the personalized KPI system
# Available KPIs are determined by user profile, not generic availability


# LEGACY ENDPOINT REMOVED: /dashboard
# Complete dashboard functionality is now provided through the personalized
# KPI system which provides profile-aware dashboard data


# === WEEK 4: PERSONALIZED DASHBOARD ENDPOINTS ===

class PersonalizedKpiRequest(BaseModel):
    """Request model for personalized KPIs."""
    user_id: str = Field(..., description="User identifier")
    profile_type: Optional[str] = Field(None, description="User profile type (auto-detected if not provided)")
    timeframe: str = Field(default="week", description="Data timeframe")
    currency: str = Field(default="all", description="Currency filter")


def get_hybrid_kpi_service_dep():
    """Dependency to get HybridKpiService instance."""
    return get_hybrid_kpi_service()


def get_smart_router_dep():
    """Dependency to get SmartQueryRouter instance."""
    return get_smart_query_router()


def get_profile_detector_dep():
    """Dependency to get ProfileDetector instance."""
    return get_profile_detector()


@dashboard_router.post("/personalized-kpis", summary="Get personalized KPIs for user")
async def get_personalized_kpis(
    request: PersonalizedKpiRequest,
    hybrid_service: get_hybrid_kpi_service_dep = Depends(),
    router_service: get_smart_router_dep = Depends(),
    profile_service: get_profile_detector_dep = Depends()
):
    """
    Get personalized KPIs based on user profile and preferences.

    **Week 4 Feature**: Routes KPIs through hybrid architecture:
    - Layer 1: Profile-aware snapshots (CEO, CFO)
    - Layer 2: Personalized cache (Trader, Operations)
    - Layer 3: Direct optimized queries (Risk_Manager)

    **Profile Detection**: Automatically detects user profile if not provided
    **Fail-Fast**: No fallbacks to mock data - real data only
    **Performance**: Optimized routing based on user profile characteristics
    """
    try:
        logger.info(f"🎯 Getting personalized KPIs for user {request.user_id}")
        logger.info(f"📅 Request timeframe: {request.timeframe}, currency: {request.currency}")

        # === PROFILE DETECTION ===
        user_profile = request.profile_type
        profile_source = "provided"

        logger.debug(f"🔍 Profile detection debug - user_id: {request.user_id}, provided_profile: {request.profile_type}")

        if not user_profile:
            logger.info(f"🔍 Auto-detecting profile for user {request.user_id}")
            profile_source = "auto_detected"

            detection_result = profile_service.detect_profile(request.user_id)
            logger.debug(f"🔍 Detection result: {detection_result}")

            if detection_result.get('detected_profile') and detection_result.get('confidence', 0) >= 0.30:
                user_profile = detection_result['detected_profile']
                logger.info(f"✅ Profile detected: {user_profile} (confidence: {detection_result['confidence']:.2f})")
                logger.debug(f"🔍 Detection analysis: {detection_result.get('analysis', {})}")
            else:
                user_profile = "Operations"  # Default profile
                profile_source = "default_fallback"
                logger.warning(f"⚠️ Profile detection failed, using default: {user_profile}")
                logger.debug(f"🔍 Detection failure reason: confidence={detection_result.get('confidence', 0):.2f}, detected={detection_result.get('detected_profile')}")

        # === PROFILE VALIDATION ===
        logger.debug(f"🔍 Validating profile: {user_profile} against supported: {SUPPORTED_PROFILES}")

        if user_profile not in SUPPORTED_PROFILES:
            logger.error(f"❌ Invalid profile type: {user_profile}")
            raise HTTPException(
                status_code=400,
                detail={
                    'error': 'invalid_profile_type',
                    'message': f'Profile type must be one of: {SUPPORTED_PROFILES}',
                    'provided': user_profile,
                    'user_id': request.user_id
                }
            )

        logger.info(f"✅ Profile validated: {user_profile} (source: {profile_source})")

        # === GET PROFILE-SPECIFIC KPIS ===
        # Get recommended KPIs for this profile
        profile_kpis = _get_profile_recommended_kpis(user_profile)
        logger.info(f"📋 Recommended KPIs for {user_profile}: {profile_kpis} (count: {len(profile_kpis)})")

        # Use all KPIs defined for the profile (no priority filtering)
        logger.info(f"🔍 Using all profile KPIs: {profile_kpis} (count: {len(profile_kpis)})")

        # === ROUTE THROUGH HYBRID ARCHITECTURE ===
        kpi_results = {}
        routing_metadata = {}

        # KPIs personalizados que devem usar HybridKpiService diretamente
        hybrid_kpis = [
            'spread_income_detailed', 'margem_liquida_operacional', 'custo_por_transacao', 'tempo_processamento_medio',
            'throughput_transacoes_hora', 'produtividade_equipe', 'fila_processamento_tamanho', 'sla_compliance_rate',
            'receita_total_mensal', 'concentracao_top10_clientes', 'margem_bruta_por_produto', 'utilizacao_limites_cliente',
            'volume_vendas_mensal', 'numero_novos_clientes'
        ]

        for kpi_id in profile_kpis:
            try:
                logger.info(f"🧮 Calculating KPI: {kpi_id}")

                if kpi_id in hybrid_kpis:
                    # DIRECT CALCULATION - bypass router complexity (FAIL FAST approach)
                    logger.info(f"🎯 Using DIRECT HybridKpiService calculation for: {kpi_id}")

                    # Get HybridKpiService and call calculator directly
                    hybrid_service = get_hybrid_kpi_service()
                    calculator_method = hybrid_service.kpi_calculators.get(kpi_id)

                    if calculator_method:
                        result = calculator_method(
                            client_id="L2M",
                            user_id=request.user_id,
                            timeframe=request.timeframe,
                            currency=request.currency,
                            profile_type=user_profile
                        )

                        if result and not result.get('error'):
                            # Convert to dashboard format
                            kpi_data = _convert_hybrid_kpi_to_dashboard_format(result, kpi_id)
                            if kpi_data:
                                kpi_results[kpi_id] = kpi_data
                                routing_metadata[kpi_id] = {
                                    'source': 'hybrid_direct',
                                    'calculation_time': result.get('calculation_time', 0)
                                }
                                logger.info(f"✅ KPI {kpi_id} calculated successfully via DIRECT method")
                            else:
                                logger.error(f"❌ KPI {kpi_id} conversion failed - FAIL FAST")
                        else:
                            logger.error(f"❌ KPI {kpi_id} calculation returned no data - FAIL FAST")
                    else:
                        logger.error(f"❌ No calculator found for KPI: {kpi_id} - FAIL FAST")
                else:
                    # Use router for legacy KPIs
                    result = router_service.route_kpi_request(
                        kpi_id=kpi_id,
                        client_id="L2M",
                        user_id=request.user_id,
                        timeframe=request.timeframe,
                        currency=request.currency,
                        profile_type=user_profile
                    )

                    if not result.get('error'):
                        # Convert to dashboard format
                        kpi_data = _convert_hybrid_kpi_to_dashboard_format(result, kpi_id)
                        kpi_results[kpi_id] = kpi_data

                        if result.get('routing_metadata'):
                            routing_metadata[kpi_id] = result['routing_metadata']
                    else:
                        logger.warning(f"⚠️ KPI {kpi_id} routing failed: {result.get('error')}")

            except Exception as e:
                logger.error(f"❌ Error calculating KPI {kpi_id}: {e}")

        # === RESPONSE ===
        response_data = {
            'user_id': request.user_id,
            'profile_type': user_profile,
            'timeframe': request.timeframe,
            'currency': request.currency,
            'kpis': list(kpi_results.values()),
            'total_kpis': len(kpi_results),
            'routing_metadata': routing_metadata,
            'personalization': {
                'profile_detected': bool(user_profile),
                'recommended_kpis': profile_kpis,
                'cache_strategy': _get_profile_cache_strategy(user_profile),
                'routing_layer': _get_profile_routing_layer(user_profile)
            }
        }

        logger.info(f"✅ Personalized KPIs response for user {request.user_id}: profile={user_profile}, kpis_count={len(kpi_results)}, profile_detected={bool(user_profile)}")
        logger.debug(f"🔍 Full response: {response_data}")

        return response_data

    except Exception as e:
        logger.error(f"❌ Error getting personalized KPIs for user {request.user_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                'error': 'personalized_kpis_error',
                'message': str(e),
                'user_id': request.user_id
            }
        )


@dashboard_router.get("/personalized-kpis/{user_id}/recommendations", summary="Get KPI recommendations for user")
async def get_kpi_recommendations(
    user_id: str,
    profile_service: get_profile_detector_dep = Depends()
):
    """
    Get KPI recommendations based on user profile.

    Returns recommended KPIs without calculating values,
    useful for dashboard configuration and setup.
    """
    try:
        logger.info(f"📋 Getting KPI recommendations for user {user_id}")

        # Detect profile
        detection_result = profile_service.detect_profile(user_id)

        profile_type = "Operations"  # Default
        if detection_result.get('detected_profile') and detection_result.get('confidence', 0) >= 0.30:
            profile_type = detection_result['detected_profile']

        # Get recommendations
        recommended_kpis = _get_profile_recommended_kpis(profile_type)

        return {
            'user_id': user_id,
            'detected_profile': profile_type,
            'confidence': detection_result.get('confidence', 0.0),
            'recommended_kpis': [
                {
                    'id': kpi_id,
                    'name': _get_kpi_display_name(kpi_id),
                    'description': _get_kpi_description(kpi_id),
                    'category': _get_kpi_category(kpi_id),
                    'priority': kpi_id in ['spread_income_detailed', 'margem_liquida_operacional']
                }
                for kpi_id in recommended_kpis
            ],
            'profile_characteristics': {
                'cache_ttl': _get_profile_cache_ttl(profile_type),
                'preferred_layer': _get_profile_routing_layer(profile_type),
                'update_frequency': _get_profile_update_frequency(profile_type)
            }
        }

    except Exception as e:
        logger.error(f"❌ Error getting KPI recommendations for user {user_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                'error': 'kpi_recommendations_error',
                'message': str(e),
                'user_id': user_id
            }
        )


# === HELPER FUNCTIONS ===

def _get_profile_recommended_kpis(profile_type: str) -> List[str]:
    """Get recommended KPIs for a profile type using the official mapping."""
    from src.config.profile_kpi_mapping import get_critical_kpis_for_profile

    # Use the official profile KPI mapping
    critical_kpis = get_critical_kpis_for_profile(profile_type)

    if critical_kpis:
        return critical_kpis

    # Fallback to basic KPIs if profile not found
    return ['custo_por_transacao', 'tempo_processamento_medio']


def _convert_hybrid_kpi_to_dashboard_format(hybrid_result: Dict[str, Any], kpi_id: str) -> Dict[str, Any]:
    """Convert hybrid KPI result to dashboard format."""
    return {
        'id': kpi_id,
        'title': hybrid_result.get('title', _get_kpi_display_name(kpi_id)),
        'description': hybrid_result.get('description', _get_kpi_description(kpi_id)),
        'currentValue': hybrid_result.get('currentValue', 0),
        'format': _get_kpi_format(kpi_id),
        'changePercent': None,  # Would need historical data
        'trend': 'stable',  # Would need trend calculation
        'chartType': _get_kpi_chart_type(kpi_id),
        'chartData': _get_real_chart_data(kpi_id, hybrid_result.get('currentValue', 0), hybrid_result.get('metadata', {})),
        'alert': None,
        'isPriority': kpi_id in ['spread_income_detailed', 'margem_liquida_operacional'],
        'order': _get_kpi_order(kpi_id),
        'category': _get_kpi_category(kpi_id),
        'unit': hybrid_result.get('unit', ''),
        'frequency': 'realtime',
        'source': hybrid_result.get('source', 'hybrid'),
        'metadata': hybrid_result.get('metadata', {})
    }


def _get_kpi_display_name(kpi_id: str) -> str:
    """Get display name for KPI."""
    names = {
        'spread_income_detailed': 'Spread Income Detalhado',
        'margem_liquida_operacional': 'Margem Líquida Operacional',
        'custo_por_transacao': 'Custo por Transação',
        'tempo_processamento_medio': 'Tempo Processamento Médio'
    }
    return names.get(kpi_id, kpi_id.replace('_', ' ').title())


def _get_kpi_description(kpi_id: str) -> str:
    """Get description for KPI."""
    descriptions = {
        'spread_income_detailed': 'Receita detalhada por spread por moeda e período',
        'margem_liquida_operacional': 'Margem operacional líquida: (Receita Spread - Custos Operacionais) / Receita Total * 100',
        'custo_por_transacao': 'Custo operacional médio por transação processada',
        'tempo_processamento_medio': 'Tempo médio de processamento de transações (em segundos)'
    }
    return descriptions.get(kpi_id, f'KPI: {kpi_id}')


def _get_kpi_category(kpi_id: str) -> str:
    """Get category for KPI."""
    categories = {
        'spread_income_detailed': 'spread',
        'margem_liquida_operacional': 'performance',
        'custo_por_transacao': 'performance',
        'tempo_processamento_medio': 'performance'
    }
    return categories.get(kpi_id, 'general')


def _get_kpi_format(kpi_id: str) -> str:
    """Get format for KPI."""
    formats = {
        'spread_income_detailed': 'currency',
        'margem_liquida_operacional': 'percentage',
        'custo_por_transacao': 'currency',
        'tempo_processamento_medio': 'number'
    }
    return formats.get(kpi_id, 'number')


def _get_kpi_chart_type(kpi_id: str) -> str:
    """Get chart type for KPI."""
    chart_types = {
        'spread_income_detailed': 'area',
        'margem_liquida_operacional': 'line',
        'custo_por_transacao': 'bar',
        'tempo_processamento_medio': 'line'
    }
    return chart_types.get(kpi_id, 'line')


def _get_kpi_order(kpi_id: str) -> int:
    """Get display order for KPI."""
    orders = {
        'spread_income_detailed': 1,
        'margem_liquida_operacional': 2,
        'custo_por_transacao': 3,
        'tempo_processamento_medio': 4
    }
    return orders.get(kpi_id, 99)


def _get_real_chart_data(kpi_id: str, current_value: float, metadata: Dict[str, Any] = None) -> List[Dict[str, Any]]:
    """Get real chart data from KPI metadata - NO MOCK DATA."""

    # Use real data from KPI calculation metadata
    if metadata and 'daily_breakdown' in metadata:
        daily_breakdown = metadata['daily_breakdown']

        # Convert daily breakdown to chart format
        chart_data = []
        for item in daily_breakdown:
            if item.get('date'):
                # Parse date and format for chart
                from datetime import datetime
                try:
                    date_obj = datetime.fromisoformat(item['date'])
                    # Extract value based on KPI type and available fields
                    value = current_value  # Default fallback

                    # Try different field names based on KPI type
                    if 'cost_per_transaction' in item:
                        value = float(item['cost_per_transaction'])
                    elif 'processing_time' in item:
                        value = float(item['processing_time'])
                    elif 'throughput' in item:
                        value = float(item['throughput'])
                    elif 'productivity' in item:
                        value = float(item['productivity'])
                    elif 'spread_income' in item:
                        value = float(item['spread_income'])
                    elif 'revenue' in item:
                        value = float(item['revenue'])
                    elif 'margin' in item:
                        value = float(item['margin'])
                    elif 'transaction_count' in item and item['transaction_count'] > 0:
                        # For some KPIs, we might need to calculate from transaction count
                        value = float(item['transaction_count'])

                    chart_data.append({
                        'name': date_obj.strftime('%d/%m'),
                        'value': value,
                        'date': item['date']
                    })
                except (ValueError, TypeError):
                    continue

        # Sort by date and return
        chart_data.sort(key=lambda x: x['date'])
        return chart_data

    # For KPIs without daily breakdown, return single point with current value
    if current_value is not None:
        from datetime import datetime
        today = datetime.now().strftime('%Y-%m-%d')
        return [{
            'name': 'Atual',
            'value': float(current_value),
            'date': today
        }]

    # FAIL FAST: No data available
    logger.warning(f"⚠️ No chart data available for KPI {kpi_id} - returning empty")
    return []


def _get_profile_cache_strategy(profile_type: str) -> Dict[str, Any]:
    """Get cache strategy for profile."""
    strategies = {
        'CEO': {'ttl': 3600, 'layer': 'snapshot', 'priority': 'accuracy'},
        'CFO': {'ttl': 1800, 'layer': 'snapshot', 'priority': 'accuracy'},
        'Risk_Manager': {'ttl': 300, 'layer': 'direct', 'priority': 'real_time'},
        'Trader': {'ttl': 60, 'layer': 'cache', 'priority': 'speed'},
        'Operations': {'ttl': 900, 'layer': 'cache', 'priority': 'efficiency'}
    }
    return strategies.get(profile_type, {'ttl': 900, 'layer': 'cache', 'priority': 'efficiency'})


def _get_profile_routing_layer(profile_type: str) -> str:
    """Get preferred routing layer for profile."""
    layers = {
        'CEO': 'snapshot',
        'CFO': 'snapshot',
        'Risk_Manager': 'direct',
        'Trader': 'cache',
        'Operations': 'cache'
    }
    return layers.get(profile_type, 'cache')


def _get_profile_cache_ttl(profile_type: str) -> int:
    """Get cache TTL for profile."""
    ttls = {
        'CEO': 3600,      # 1 hour
        'CFO': 1800,      # 30 minutes
        'Risk_Manager': 300,  # 5 minutes
        'Trader': 60,     # 1 minute
        'Operations': 900  # 15 minutes
    }
    return ttls.get(profile_type, 900)


def _get_profile_update_frequency(profile_type: str) -> str:
    """Get update frequency for profile."""
    frequencies = {
        'CEO': 'hourly',
        'CFO': 'every_30_minutes',
        'Risk_Manager': 'every_5_minutes',
        'Trader': 'realtime',
        'Operations': 'every_15_minutes'
    }
    return frequencies.get(profile_type, 'every_15_minutes')
